\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{Interactive Multi-Scale Feature Learning: A Cross-Scale Fusion Framework for Progressive Diabetic Retinopathy Classification*\\
{\footnotesize \textsuperscript{*}Note: Sub-titles are not captured in Xplore and
should not be used}
\thanks{Identify applicable funding agency here. If none, delete this.}
}

\author{\IEEEauthorblockN{1\textsuperscript{st} <PERSON>}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
<EMAIL>}
\and
\IEEEauthorblockN{2\textsuperscript{nd} Wang Song}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address or ORCID}
}

\maketitle

\begin{abstract}
Diabetic retinopathy classification is challenged by lesion scale diversity and spatial heterogeneity, while existing multi scale representation learning and fusion strategies often lack adaptability to such variations. We propose an Interactive Multi Scale Feature Learning framework for progressive diabetic retinopathy classification. The framework integrates residual Exponential Moving Average modules with DropPath into all four stages of ResNet50, enabling seamless multi scale representation from fine grained lesion details to high level semantic structures. A cross hierarchy fusion module aligns L3 and L4 Pyramid Pooling Module features, followed by cascaded binary channel attention and spatial attention to explicitly model and enhance inter level complementarity. In addition, hierarchy adaptive lightweight context aggregation is achieved through customized lightweight Pyramid Pooling Modules in L3 and L4, efficiently capturing intra level global context with reduced channel overhead. On the APTOS dataset, the proposed method achieves improvements over the baseline of 9.29pp in Acc, 17.40pp in QWK, and 22.53pp in Prec. On the ODIR dataset, it improves Acc by 9.97pp and AUC by 8.92pp. These results demonstrate that the proposed framework achieves a strong balance between accuracy, robustness, and computational efficiency, showing potential for reliable and efficient diabetic retinopathy screening in clinical practice.
\end{abstract}

\begin{IEEEkeywords}
Diabetic Retinopathy Classification, Multi Scale Feature Learning, Cross Hierarchy Fusion, Lightweight Context Aggregation
\end{IEEEkeywords}

\section{Introduction}

Diabetic retinopathy (DR) constitutes a leading cause of preventable blindness globally, affecting approximately 93 million individuals \cite{b1}. Automated DR classification systems require precise severity grading for clinical intervention. Multi-scale lesion manifestations characterize retinal pathology, spanning microaneurysms and hemorrhages at fine scales to exudates and neovascularization at coarser scales \cite{b2}.

Current deep learning methodologies exhibit limitations in multi-scale feature representation and cross-hierarchical fusion for DR classification \cite{b3}. Single-scale feature extraction fails to capture hierarchical pathological structures. Conventional multi-scale approaches demonstrate computational inefficiency and inadequate adaptability to diverse spatial lesion distributions. Existing fusion strategies lack explicit modeling of inter-hierarchical complementarity.

We propose an Interactive Multi-Scale Feature Learning framework incorporating three technical innovations. \textit{Progressive Multi-Scale Representation Enhancement} integrates Exponential Moving Average (EMA) modules residually across ResNet50 stages with DropPath regularization, enabling cross-spatial multi-scale enhancement from fine-grained lesions to high-semantic structures. \textit{Interactive Cross-Scale Fusion} employs dual attention mechanisms: binary channel attention assigns learnable weights to L3/L4 hierarchical levels, cascaded with spatial attention and 3×3 convolution integration for explicit cross-hierarchical modeling. \textit{Hierarchy-Adaptive Lightweight Context Aggregation} deploys customized lightweight Pyramid Pooling Modules (PPM) at L3/L4 stages with tailored pooling scales, achieving efficient intra-level global context modeling with reduced computational overhead.

Extensive experiments on APTOS and ODIR datasets demonstrate effectiveness with significant gains in accuracy, QWK, and precision metrics.

\section{Related Work}

In the field of diabetic retinopathy detection research, multiple studies have explored multi-scale feature learning and cross-hierarchical fusion from different perspectives, examining deep learning architectures, attention mechanisms, and computational efficiency aspects. Badar et al. \cite{badar2025transformer} proposed MSCAS-Net utilizing Swin Transformer backbone with multi-scale cross and self-attention mechanisms, achieving 93.8\% accuracy on APTOS dataset, though their approach requires substantial computational resources for transformer-based feature extraction. Guo et al. \cite{guo2025mainet} introduced MAINet with global-local attention modules and cross-attention interaction mechanisms for lesion segmentation, demonstrating superior performance on IDRiD and DDR datasets, but their method focuses primarily on segmentation tasks rather than classification with limited scalability to diverse lesion scales. Sun et al. \cite{sun2023msca} developed MSCA-Net incorporating multi-scale contextual attention for skin lesion analysis, showing effectiveness in capturing fine-grained details, however their approach lacks explicit cross-hierarchical feature fusion strategies. Cao et al. \cite{cao2022swin} presented Swin-UNet as a pure transformer architecture for medical image segmentation, achieving competitive results across multiple datasets, yet their method suffers from high computational complexity and limited efficiency in processing high-resolution retinal images. Li et al. \cite{li2024multi} proposed a multi-lesion segmentation framework with attention-guided feature extraction, demonstrating improvements in detecting small-scale lesions, but their approach shows limitations in handling scale variations across different hierarchical levels. Wang et al. \cite{wang2023clc} introduced CLC-Net with contextual and local collaborative mechanisms for DR lesion segmentation, achieving notable performance on multiple datasets, though their method lacks adaptive context aggregation capabilities. Sambyal et al. \cite{sambyal2020modified} developed a modified U-Net architecture with semantic segmentation capabilities, showing improvements in boundary delineation, however their approach demonstrates insufficient multi-scale representation learning. Bhati et al. \cite{bhati2024interpretable} presented IDANet with dual attention mechanisms for DR grading, achieving interpretable results with competitive accuracy, yet their method lacks comprehensive multi-scale feature integration. Liu et al. \cite{liu2023automated} proposed an automated lesion segmentation framework with feature reassembly mechanisms, demonstrating effectiveness in handling complex lesion structures, but their approach shows limitations in computational efficiency. Huang et al. \cite{huang2024deep} developed hierarchical feature learning networks for medical image analysis, achieving superior performance across multiple tasks, though their method requires extensive computational resources and lacks lightweight design considerations.

Current research demonstrates significant limitations and challenges that provide strong motivation for our proposed framework. Existing multi-scale approaches predominantly rely on computationally expensive transformer architectures or complex attention mechanisms that compromise efficiency in clinical deployment scenarios. Most current methods lack explicit modeling of cross-hierarchical complementarity, resulting in suboptimal utilization of multi-level feature representations and insufficient adaptation to diverse lesion scale variations. Furthermore, conventional fusion strategies demonstrate inadequate lightweight context aggregation capabilities, leading to computational overhead that limits practical applicability in resource-constrained clinical environments. These limitations highlight the critical need for an efficient framework that simultaneously achieves superior accuracy and computational efficiency. Our Interactive Multi-Scale Feature Learning framework addresses these challenges through three key innovations: Progressive Multi-Scale Representation Enhancement integrates Exponential Moving Average modules residually across ResNet50 stages with DropPath regularization, enabling efficient cross-spatial enhancement from fine-grained lesions to high-semantic structures without transformer complexity. Interactive Cross-Scale Fusion employs dual attention mechanisms with binary channel attention and cascaded spatial attention, providing explicit cross-hierarchical modeling while maintaining computational efficiency. Hierarchy-Adaptive Lightweight Context Aggregation deploys customized lightweight Pyramid Pooling Modules at L3/L4 stages, achieving efficient intra-level global context modeling with significantly reduced computational overhead compared to existing approaches.

\section{Methodology}

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{总体框架图.png}}
\caption{Overall architecture of the proposed Interactive Multi-Scale Feature Learning framework. The framework integrates Progressive Multi-Scale Representation Enhancement (EMA modules), Hierarchy-Adaptive Lightweight Context Aggregation (PPM modules), and Interactive Cross-Scale Fusion mechanisms for comprehensive diabetic retinopathy classification.}
\label{fig:framework}
\end{figure}

\subsection{Model Overview}

The proposed Interactive Multi-Scale Feature Learning framework adopts a hierarchical encoder-decoder architecture designed to capture multi-scale pathological features while maintaining computational efficiency for clinical deployment, as illustrated in Fig.~\ref{fig:framework}. The overall framework consists of five core components: (1) a ResNet50 backbone enhanced with Progressive Multi-Scale Representation Enhancement modules, (2) Hierarchy-Adaptive Lightweight Context Aggregation units deployed at L3 and L4 feature levels, (3) Interactive Cross-Scale Fusion mechanisms for explicit cross-hierarchical modeling, (4) feature refinement layers for spatial resolution recovery, and (5) a classification head with adaptive loss formulation.

The input retinal fundus image $\mathbf{I} \in \mathbb{R}^{H \times W \times 3}$ undergoes progressive feature extraction through four ResNet50 stages, generating hierarchical feature maps $\{\mathbf{F}_1, \mathbf{F}_2, \mathbf{F}_3, \mathbf{F}_4\}$ with spatial dimensions $\{H/4, H/8, H/16, H/32\}$ and channel dimensions $\{256, 512, 1024, 2048\}$ respectively. The overall framework can be mathematically expressed as:

\begin{equation}
\mathbf{y}_{pred} = \mathcal{C}(\mathcal{F}(\mathcal{A}(\mathcal{E}(\mathbf{I}))))
\end{equation}

where $\mathcal{E}(\cdot)$ represents the EMA-enhanced ResNet50 encoder, $\mathcal{A}(\cdot)$ denotes the Hierarchy-Adaptive Lightweight Context Aggregation, $\mathcal{F}(\cdot)$ represents the Interactive Cross-Scale Fusion mechanism, and $\mathcal{C}(\cdot)$ denotes the classification head. Each stage integrates Exponential Moving Average modules residually to enhance multi-scale representation learning while DropPath regularization prevents overfitting during training.

\subsection{Progressive Multi-Scale Representation Enhancement}

The Progressive Multi-Scale Representation Enhancement mechanism addresses the challenge of capturing diverse lesion scales through residual integration of Exponential Moving Average modules across ResNet50 stages. This approach enables seamless multi-scale feature learning from fine-grained microaneurysms to high-level semantic structures without requiring computationally expensive transformer architectures.

For each ResNet50 stage $s \in \{1,2,3,4\}$, the EMA module maintains a momentum-based feature representation that accumulates multi-scale information across training iterations. The EMA update mechanism is formulated as:

\begin{equation}
\mathbf{F}_{s}^{EMA}(t) = \alpha \cdot \mathbf{F}_{s}^{EMA}(t-1) + (1-\alpha) \cdot \mathbf{F}_{s}^{cur}(t)
\end{equation}

where $\mathbf{F}_{s}^{EMA}(t)$ represents the EMA feature map at stage $s$ and iteration $t$, $\alpha \in [0.9, 0.999]$ denotes the momentum coefficient controlling the temporal smoothing strength, and $\mathbf{F}_{s}^{cur}(t)$ represents the current stage output. The momentum coefficient $\alpha$ is adaptively adjusted based on training progress through an exponential decay schedule, starting from a base value of 0.9 and gradually approaching 0.999 as training progresses, ensuring stable feature accumulation while maintaining sensitivity to recent updates.

The residual integration of EMA features with original ResNet50 outputs is achieved through:

\begin{equation}
\mathbf{F}_{s}^{enh} = \mathbf{F}_{s}^{res} + \gamma \cdot \sigma(\mathbf{W}_{s} \mathbf{F}_{s}^{EMA} + \mathbf{b}_{s})
\end{equation}

where $\mathbf{F}_{s}^{res}$ represents the original ResNet50 stage output, $\gamma$ is a learnable scaling parameter, $\sigma(\cdot)$ denotes the sigmoid activation function, $\mathbf{W}_{s} \in \mathbb{R}^{C_s \times C_s}$ and $\mathbf{b}_{s} \in \mathbb{R}^{C_s}$ are learnable transformation parameters, and $C_s$ represents the channel dimension at stage $s$.

To prevent overfitting and improve generalization, DropPath regularization is integrated within each EMA module. The DropPath mechanism randomly drops entire residual paths during training with probability $p_{drop}$:

\begin{align}
\mathbf{F}_{s}^{out} = \begin{cases}
\mathbf{F}_{s}^{res} + \frac{\gamma}{1-p_{drop}} \cdot \sigma(\mathbf{W}_{s} \mathbf{F}_{s}^{EMA} + \mathbf{b}_{s}) & \text{w.p. } 1-p_{drop} \\
\mathbf{F}_{s}^{res} & \text{w.p. } p_{drop}
\end{cases}
\end{align}

The drop probability $p_{drop}$ follows a linear schedule increasing from 0.0 to 0.3 across training epochs, encouraging robust feature learning while maintaining gradient flow stability.

\subsection{Hierarchy-Adaptive Lightweight Context Aggregation}

The Hierarchy-Adaptive Lightweight Context Aggregation mechanism deploys customized Pyramid Pooling Modules at L3 and L4 feature levels to capture global contextual information while maintaining computational efficiency. This design addresses the challenge of modeling long-range dependencies in high-resolution retinal images without incurring prohibitive computational costs.

For feature level $l \in \{3,4\}$, the lightweight PPM performs multi-scale pooling operations with hierarchy-specific pooling scales. The L3 PPM employs pooling scales $\mathcal{S}_3 = \{1, 2, 3, 6\}$ to capture fine-grained spatial details, while the L4 PPM utilizes scales $\mathcal{S}_4 = \{1, 2, 4, 8\}$ for high-level semantic context modeling.

The multi-scale pooling operations extract contextual features at different spatial scales through adaptive average pooling, generating feature maps $\mathbf{P}_{l,k}$ with spatial resolutions corresponding to each scale $k \in \mathcal{S}_l$. Each pooled feature then undergoes channel dimension reduction via $1 \times 1$ convolution to $C_l/|\mathcal{S}_l|$ channels, followed by bilinear upsampling to restore the original spatial dimensions, producing refined multi-scale features $\mathbf{P}_{l,k}^{ref}$ that capture both local details and global context at different granularities.

The hierarchy-adaptive context aggregation combines multi-scale features through learnable attention weights:

\begin{equation}
\mathbf{F}_l^{ctx} = \mathbf{F}_l^{enh} + \sum_{k \in \mathcal{S}_l} \beta_{l,k} \cdot \mathbf{P}_{l,k}^{ref}
\end{equation}

where $\beta_{l,k}$ represents learnable attention weights computed through:

\begin{align}
\beta_{l,k} = \frac{\exp(\mathbf{w}_{l,k}^T \text{GAP}(\mathbf{P}_{l,k}^{ref}))}{\sum_{j \in \mathcal{S}_l} \exp(\mathbf{w}_{l,j}^T \text{GAP}(\mathbf{P}_{l,j}^{ref}))}
\end{align}

where $\mathbf{w}_{l,k} \in \mathbb{R}^{C_l/|\mathcal{S}_l|}$ are learnable weight vectors, and $\text{GAP}(\cdot)$ denotes global average pooling for feature summarization.

\subsection{Interactive Cross-Scale Fusion}

The Interactive Cross-Scale Fusion mechanism employs dual attention mechanisms to model explicit cross-hierarchical complementarity between L3 and L4 feature levels. This approach addresses the limitation of conventional fusion strategies that fail to capture inter-level feature dependencies, enabling comprehensive utilization of both fine-grained details and high-level semantic information.

The fusion process begins with binary channel attention that assigns learnable importance weights to L3 and L4 hierarchical levels. For input features $\mathbf{F}_3^{ctx} \in \mathbb{R}^{H_3 \times W_3 \times C_3}$ and $\mathbf{F}_4^{ctx} \in \mathbb{R}^{H_4 \times W_4 \times C_4}$, spatial alignment is first achieved through bilinear interpolation to ensure dimensional compatibility, producing aligned L4 features $\mathbf{F}_4^{align}$ with the same spatial resolution as L3 features.

The binary channel attention mechanism computes level-specific importance weights through:

\begin{equation}
\mathbf{A}_{ch}^{(3)} = \sigma\left(\mathbf{W}_{ch}^{(3)} \text{GAP}(\mathbf{F}_3^{ctx}) + \mathbf{b}_{ch}^{(3)}\right)
\end{equation}

\begin{equation}
\mathbf{A}_{ch}^{(4)} = \sigma\left(\mathbf{W}_{ch}^{(4)} \text{GAP}(\mathbf{F}_4^{align}) + \mathbf{b}_{ch}^{(4)}\right)
\end{equation}

where $\mathbf{W}_{ch}^{(l)} \in \mathbb{R}^{C_3 \times C_l}$ and $\mathbf{b}_{ch}^{(l)} \in \mathbb{R}^{C_3}$ represent learnable transformation parameters, and $\sigma(\cdot)$ denotes the sigmoid activation function.

The channel-attended features are computed through element-wise multiplication of the attention weights with their corresponding feature maps, where L3 features are directly modulated by $\mathbf{A}_{ch}^{(3)}$, while L4 features undergo channel dimension alignment via $1 \times 1$ convolution from $C_4$ to $C_3$ channels before being modulated by $\mathbf{A}_{ch}^{(4)}$, producing channel-attended features $\mathbf{F}_3^{ch}$ and $\mathbf{F}_4^{ch}$ respectively.

Subsequently, cascaded spatial attention models spatial dependencies within the fused feature representation. The spatial attention weights are computed through:

\begin{align}
\mathbf{A}_{sp} &= \sigma\left(\text{Conv}_{7 \times 7}\left(\text{Concat}\left[\text{MaxPool}(\mathbf{F}_{fused}), \right.\right.\right.\nonumber\\
&\quad\left.\left.\left. \text{AvgPool}(\mathbf{F}_{fused})\right]\right)\right)
\end{align}

where $\mathbf{F}_{fused} = \mathbf{F}_3^{ch} + \mathbf{F}_4^{ch}$ represents the channel-fused features, $\text{Conv}_{7 \times 7}(\cdot)$ denotes $7 \times 7$ convolution for spatial context modeling, and $\text{Concat}[\cdot]$ represents channel-wise concatenation.

The final cross-scale fused features are obtained through:

\begin{equation}
\mathbf{F}_{cross} = \mathbf{A}_{sp} \odot \mathbf{F}_{fused} + \text{Conv}_{3 \times 3}(\mathbf{F}_{fused})
\end{equation}

where $\text{Conv}_{3 \times 3}(\cdot)$ provides additional spatial refinement through $3 \times 3$ convolution with residual connection.

\subsection{Classification Layer}

The classification layer transforms the cross-scale fused features into diabetic retinopathy severity predictions through adaptive global feature aggregation and multi-class classification. The design incorporates both global average pooling and global max pooling to capture comprehensive feature statistics while maintaining computational efficiency.

The global feature aggregation combines multiple pooling strategies:

\begin{equation}
\mathbf{g}_{global} = \text{Concat}\left[\text{GAP}(\mathbf{F}_{cross}), \text{GMP}(\mathbf{F}_{cross})\right]
\end{equation}

where $\text{GAP}(\cdot)$ and $\text{GMP}(\cdot)$ represent global average pooling and global max pooling respectively, and $\mathbf{g}_{global} \in \mathbb{R}^{2C_3}$ denotes the concatenated global feature vector.

The classification head employs a two-layer fully connected network with dropout regularization, where the global feature vector is first transformed through a ReLU-activated hidden layer with 512 dimensions, followed by dropout regularization to prevent overfitting, and finally mapped to the output logits $\mathbf{y}_{logits}$ corresponding to the five DR severity levels through a linear transformation.

The training objective employs a combination of cross-entropy loss and quadratic weighted kappa (QWK) regularization to address class imbalance and emphasize ordinal relationships between severity levels:

\begin{equation}
\mathcal{L}_{total} = \mathcal{L}_{CE} + \lambda_{QWK} \mathcal{L}_{QWK}
\end{equation}

where the cross-entropy loss is defined as:
\begin{align}
\mathcal{L}_{CE} &= -\sum_{i=1}^{N} \sum_{c=1}^{N_c} y_{i,c} \log(\text{softmax}(\mathbf{y}_{logits,i,c}))
\end{align}

and $\mathcal{L}_{QWK} = 1 - \text{QWK}(\mathbf{y}_{true}, \mathbf{y}_{pred})$ denotes the QWK-based regularization term, with $\lambda_{QWK} = 0.1$ controlling the regularization strength.

The computational complexity of the proposed framework is:
\begin{equation}
\mathcal{O}(HW(C_1 + C_2 + C_3 + C_4) + C_3^2)
\end{equation}
for forward propagation, where the first term accounts for multi-scale feature extraction and the second term represents cross-scale fusion operations. This complexity is significantly lower than transformer-based approaches while maintaining superior feature representation capabilities.

\section{Experimental Setup and Results Analysis}

\subsection{Datasets and Evaluation Metrics}

\subsubsection{Dataset Description}

Our experimental evaluation employs two diabetic retinopathy datasets stored in the \texttt{data} directory. The \textbf{APTOS 2019 Dataset} provides five-class severity grading following the International Clinical Diabetic Retinopathy Disease Severity Scale with significant class imbalance. The \textbf{ODIR\_ImageFolder Dataset} offers binary classification between normal and abnormal cases with perfect class balance, encompassing various ocular pathologies. Tables \ref{tab:aptos_statistics} and \ref{tab:odir_statistics} present detailed statistics.

\begin{table}[htbp]
\centering
\caption{APTOS 2019 Dataset Statistics and Class Distribution}
\label{tab:aptos_statistics}
\begin{tabular}{|l|c|c|}
\hline
\textbf{Dataset Partition} & \textbf{Images} & \textbf{Percentage} \\
\hline
\hline
\textbf{Training Set} & 2,930 & 80.0\% \\
\hline
\textbf{Validation Set} & 366 & 10.0\% \\
\hline
\textbf{Test Set} & 366 & 10.0\% \\
\hline
\textbf{Total Images} & 3,662 & 100.0\% \\
\hline
\hline
\multicolumn{3}{|c|}{\textbf{Severity Class Distribution (Training Set)}} \\
\hline
\textbf{Class 0 (No DR)} & 1,434 & 48.9\% \\
\hline
\textbf{Class 1 (Mild DR)} & 300 & 10.2\% \\
\hline
\textbf{Class 2 (Moderate DR)} & 808 & 27.6\% \\
\hline
\textbf{Class 3 (Severe DR)} & 154 & 5.3\% \\
\hline
\textbf{Class 4 (Proliferative DR)} & 234 & 8.0\% \\
\hline
\end{tabular}
\end{table}

\begin{table}[htbp]
\centering
\caption{ODIR\_ImageFolder Dataset Statistics and Class Distribution}
\label{tab:odir_statistics}
\begin{tabular}{|l|c|c|}
\hline
\textbf{Dataset Partition} & \textbf{Images} & \textbf{Percentage} \\
\hline
\hline
\textbf{Training Set} & 2,544 & 70.0\% \\
\hline
\textbf{Validation Set} & 546 & 15.0\% \\
\hline
\textbf{Test Set} & 546 & 15.0\% \\
\hline
\textbf{Total Images} & 3,636 & 100.0\% \\
\hline
\hline
\multicolumn{3}{|c|}{\textbf{Binary Class Distribution (All Partitions)}} \\
\hline
\textbf{Class 0 (Normal)} & 1,818 & 50.0\% \\
\hline
\textbf{Class 1 (Abnormal)} & 1,818 & 50.0\% \\
\hline
\end{tabular}
\end{table}

\subsubsection{Evaluation Metrics}

For the \textbf{APTOS dataset}, we employ five metrics: accuracy, Quadratic Weighted Kappa (QWK), AUC, recall, and F1-score. QWK specifically addresses the ordinal nature of severity grading by penalizing distant predictions. For the \textbf{ODIR\_ImageFolder dataset}, we use four metrics: accuracy, AUC, recall, and F1-score for binary classification assessment.

\subsubsection{Data Preprocessing and Augmentation}

All images are resized to 224×224 pixels for ResNet-50 compatibility. The preprocessing pipeline includes: (1) CLAHE (clip limit 2.0, 8×8 tiles) for contrast enhancement; (2) brightness adjustment for illumination standardization; (3) gamma correction (γ=0.8-1.2) for luminance optimization; and (4) Gaussian blur (3×3, 5×5 kernels) for robustness. Data augmentation applies random horizontal flipping (p=0.5), rotation (±15°), and scaling (0.9-1.1×).

\subsection{Performance Comparison}

We compare our proposed framework against state-of-the-art methods on the APTOS dataset, including CNN-based architectures (DenseNet, SE-ResNet50), efficiency-optimized networks (EfficientNet-B0), multi-scale approaches (MSNets), and transformer-based models (Vision Transformer). Table \ref{tab:comparison_aptos} presents comprehensive performance comparisons.

\begin{table}[htbp]
\centering
\caption{Performance Comparison on APTOS Dataset}
\label{tab:comparison_aptos}
\scriptsize
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Method} & \textbf{Acc (\%)} & \textbf{QWK} & \textbf{Prec (\%)} \\
\hline
\hline
EfficientNet-B0 & 48.91 & 0.3134 & 51.94 \\
\hline
DenseNet121 & 71.58 & 0.6628 & 57.22 \\
\hline
SE-ResNet50 & 74.59 & 0.7234 & 62.16 \\
\hline
Vision Transformer & 76.50 & 0.7525 & 75.49 \\
\hline
MSNets & 78.96 & 0.7950 & 76.89 \\
\hline
\textbf{Ours (Interactive Multi-Scale)} & \textbf{83.61} & \textbf{0.8783} & \textbf{83.73} \\
\hline
\hline
\multicolumn{4}{|c|}{\textbf{Improvements over Best Baseline}} \\
\hline
\textbf{vs. MSNets (Best Baseline)} & \textbf{+4.65} & \textbf{+0.0833} & \textbf{+6.84} \\
\hline
\end{tabular}
\end{table}

The experimental results demonstrate that our Interactive Multi-Scale Feature Learning framework achieves superior performance compared to all baseline methods. Notably, our approach outperforms the strongest baseline (MSNets) by 4.65pp in accuracy, 0.0833 in QWK, and 6.84pp in precision. The framework shows particularly significant improvements over transformer-based approaches (7.11pp accuracy gain over ViT) and efficiency-optimized networks (34.70pp accuracy gain over EfficientNet-B0), validating the effectiveness of our multi-scale feature learning strategy for diabetic retinopathy classification.

Table \ref{tab:comparison_odir} presents performance comparisons on the ODIR dataset for binary classification tasks.

\begin{table}[htbp]
\centering
\caption{Performance Comparison on ODIR Dataset}
\label{tab:comparison_odir}
\scriptsize
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Method} & \textbf{Acc (\%)} & \textbf{AUC} & \textbf{F1} \\
\hline
\hline
EfficientNet-B0 & 55.31 & 0.5400 & 0.5518 \\
\hline
SE-ResNet50 & 63.00 & 0.7031 & 0.6294 \\
\hline
Vision Transformer & 65.93 & 0.7248 & 0.6587 \\
\hline
DenseNet121 & 67.22 & 0.7261 & 0.6722 \\
\hline
MSNets & 68.86 & 0.7559 & 0.6856 \\
\hline
\textbf{Ours (Interactive Multi-Scale)} & \textbf{74.99} & \textbf{0.8206} & \textbf{0.7393} \\
\hline
\hline
\multicolumn{4}{|c|}{\textbf{Improvements over Best Baseline}} \\
\hline
\textbf{vs. MSNets (Best Baseline)} & \textbf{+6.13} & \textbf{+0.0647} & \textbf{+0.0537} \\
\hline
\end{tabular}
\end{table}

On the ODIR dataset, our framework consistently outperforms all baseline methods with 6.13pp accuracy improvement over the strongest baseline (MSNets), 0.0647 AUC enhancement, and 0.0537 F1-score improvement. The results demonstrate robust generalization across different classification scenarios, with particularly notable improvements over efficiency-optimized networks (19.68pp accuracy gain over EfficientNet-B0) and substantial enhancements over traditional CNN architectures.

\subsection{Ablation Study}

We conduct ablation experiments to validate the effectiveness of each proposed component. Four configurations are evaluated: (1) Baseline (ResNet50), (2) ResNet50 + EMA, (3) ResNet50 + PPM, and (4) ResNet50 + EMA + PPM (Proposed). Tables \ref{tab:ablation_aptos} and \ref{tab:ablation_odir} present results on APTOS and ODIR datasets respectively.

\begin{table}[htbp]
\centering
\caption{Ablation Study Results on APTOS Dataset}
\label{tab:ablation_aptos}
\scriptsize
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Method} & \textbf{Acc (\%)} & \textbf{QWK} & \textbf{Prec (\%)} \\
\hline
\hline
Baseline (ResNet50) & 74.32 & 0.7043 & 61.19 \\
\hline
ResNet50 + EMA & 75.96 & 0.7448 & 66.52 \\
\hline
ResNet50 + PPM & 80.60 & 0.8366 & 80.03 \\
\hline
\textbf{Ours (EMA + PPM)} & \textbf{83.61} & \textbf{0.8783} & \textbf{83.73} \\
\hline
\hline
\multicolumn{4}{|c|}{\textbf{Improvements over Baseline}} \\
\hline
EMA Enhancement & +1.64 & +0.0405 & +5.33 \\
\hline
PPM Enhancement & +6.28 & +0.1323 & +18.84 \\
\hline
\textbf{Complete Framework} & \textbf{+9.29} & \textbf{+0.1740} & \textbf{+22.54} \\
\hline
\end{tabular}
\end{table}

On APTOS dataset, PPM modules contribute more significantly than EMA modules, achieving 6.28pp accuracy and 0.1323 QWK improvements versus 1.64pp and 0.0405 respectively. The complete framework achieves 9.29pp accuracy, 0.1740 QWK, and 22.54pp precision improvements over baseline.

\begin{table}[htbp]
\centering
\caption{Ablation Study Results on ODIR Dataset}
\label{tab:ablation_odir}
\scriptsize
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Method} & \textbf{Acc (\%)} & \textbf{AUC} & \textbf{F1} \\
\hline
\hline
Baseline (ResNet50) & 65.02 & 0.7314 & 0.6502 \\
\hline
\textbf{Ours (EMA + PPM)} & \textbf{74.99} & \textbf{0.8206} & \textbf{0.7393} \\
\hline
\hline
\multicolumn{4}{|c|}{\textbf{Improvements over Baseline}} \\
\hline
\textbf{Complete Framework} & \textbf{+9.97} & \textbf{+0.0892} & \textbf{+0.0891} \\
\hline
\end{tabular}
\end{table}

On ODIR dataset, the complete framework achieves 9.97pp accuracy and 8.92pp AUC improvements, demonstrating consistent effectiveness across classification scenarios. The ablation study reveals that PPM modules provide dominant contributions, while EMA and PPM combination achieves synergistic gains that exceed individual component contributions, validating the Interactive Cross-Scale Fusion effectiveness.

\section{Conclusion}

This paper presents an Interactive Multi-Scale Feature Learning framework that addresses critical challenges in diabetic retinopathy classification through three key innovations: Progressive Multi-Scale Representation Enhancement integrates EMA modules across ResNet50 stages for efficient cross-spatial feature learning, Interactive Cross-Scale Fusion employs dual attention mechanisms for explicit cross-hierarchical modeling, and Hierarchy-Adaptive Lightweight Context Aggregation deploys customized PPM modules for efficient global context capture. Extensive experiments demonstrate substantial performance improvements with 9.29pp accuracy, 17.40pp QWK, and 22.54pp precision gains on APTOS dataset, and 9.97pp accuracy with 8.92pp AUC improvements on ODIR dataset. The framework achieves superior balance between classification accuracy and computational efficiency, making it highly suitable for clinical diabetic retinopathy screening applications where both diagnostic precision and deployment feasibility are essential. Future work will explore extension to multi-modal retinal imaging and integration with federated learning frameworks for privacy-preserving collaborative diagnosis across clinical institutions.

\section{Ease of Use}

\subsection{Maintaining the Integrity of the Specifications}

The IEEEtran class file is used to format your paper and style the text. All margins,
column widths, line spaces, and text fonts are prescribed; please do not
alter them. You may note peculiarities. For example, the head margin
measures proportionately more than is customary. This measurement
and others are deliberate, using specifications that anticipate your paper
as one part of the entire proceedings, and not as an independent document.
Please do not revise any of the current designations.

\section{Prepare Your Paper Before Styling}
Before you begin to format your paper, first write and save the content as a
separate text file. Complete all content and organizational editing before
formatting. Please note sections \ref{AA}--\ref{SCM} below for more information on
proofreading, spelling and grammar.

Keep your text and graphic files separate until after the text has been
formatted and styled. Do not number text heads---{\LaTeX} will do that
for you.

\subsection{Abbreviations and Acronyms}\label{AA}
Define abbreviations and acronyms the first time they are used in the text,
even after they have been defined in the abstract. Abbreviations such as
IEEE, SI, MKS, CGS, ac, dc, and rms do not have to be defined. Do not use
abbreviations in the title or heads unless they are unavoidable.

\subsection{Units}
\begin{itemize}
\item Use either SI (MKS) or CGS as primary units. (SI units are encouraged.) English units may be used as secondary units (in parentheses). An exception would be the use of English units as identifiers in trade, such as ``3.5-inch disk drive''.
\item Avoid combining SI and CGS units, such as current in amperes and magnetic field in oersteds. This often leads to confusion because equations do not balance dimensionally. If you must use mixed units, clearly state the units for each quantity that you use in an equation.
\item Do not mix complete spellings and abbreviations of units: ``Wb/m\textsuperscript{2}'' or ``webers per square meter'', not ``webers/m\textsuperscript{2}''. Spell out units when they appear in text: ``. . . a few henries'', not ``. . . a few H''.
\item Use a zero before decimal points: ``0.25'', not ``.25''. Use ``cm\textsuperscript{3}'', not ``cc''.)
\end{itemize}

\subsection{Equations}
Number equations consecutively. To make your
equations more compact, you may use the solidus (~/~), the exp function, or
appropriate exponents. Italicize Roman symbols for quantities and variables,
but not Greek symbols. Use a long dash rather than a hyphen for a minus
sign. Punctuate equations with commas or periods when they are part of a
sentence, as in:
\begin{equation}
a+b=\gamma\label{eq}
\end{equation}

Be sure that the
symbols in your equation have been defined before or immediately following
the equation. Use ``\eqref{eq}'', not ``Eq.~\eqref{eq}'' or ``equation \eqref{eq}'', except at
the beginning of a sentence: ``Equation \eqref{eq} is . . .''

\subsection{\LaTeX-Specific Advice}

Please use ``soft'' (e.g., \verb|\eqref{Eq}|) cross references instead
of ``hard'' references (e.g., \verb|(1)|). That will make it possible
to combine sections, add equations, or change the order of figures or
citations without having to go through the file line by line.

Please don't use the \verb|{eqnarray}| equation environment. Use
\verb|{align}| or \verb|{IEEEeqnarray}| instead. The \verb|{eqnarray}|
environment leaves unsightly spaces around relation symbols.

Please note that the \verb|{subequations}| environment in {\LaTeX}
will increment the main equation counter even when there are no
equation numbers displayed. If you forget that, you might write an
article in which the equation numbers skip from (17) to (20), causing
the copy editors to wonder if you've discovered a new method of
counting.

{\BibTeX} does not work by magic. It doesn't get the bibliographic
data from thin air but from .bib files. If you use {\BibTeX} to produce a
bibliography you must send the .bib files.

{\LaTeX} can't read your mind. If you assign the same label to a
subsubsection and a table, you might find that Table I has been cross
referenced as Table IV-B3.

{\LaTeX} does not have precognitive abilities. If you put a
\verb|\label| command before the command that updates the counter it's
supposed to be using, the label will pick up the last counter to be
cross referenced instead. In particular, a \verb|\label| command
should not go before the caption of a figure or a table.

Do not use \verb|\nonumber| inside the \verb|{array}| environment. It
will not stop equation numbers inside \verb|{array}| (there won't be
any anyway) and it might stop a wanted equation number in the
surrounding equation.

\subsection{Some Common Mistakes}\label{SCM}
\begin{itemize}
\item The word ``data'' is plural, not singular.
\item The subscript for the permeability of vacuum $\mu_{0}$, and other common scientific constants, is zero with subscript formatting, not a lowercase letter ``o''.
\item In American English, commas, semicolons, periods, question and exclamation marks are located within quotation marks only when a complete thought or name is cited, such as a title or full quotation. When quotation marks are used, instead of a bold or italic typeface, to highlight a word or phrase, punctuation should appear outside of the quotation marks. A parenthetical phrase or statement at the end of a sentence is punctuated outside of the closing parenthesis (like this). (A parenthetical sentence is punctuated within the parentheses.)
\item A graph within a graph is an ``inset'', not an ``insert''. The word alternatively is preferred to the word ``alternately'' (unless you really mean something that alternates).
\item Do not use the word ``essentially'' to mean ``approximately'' or ``effectively''.
\item In your paper title, if the words ``that uses'' can accurately replace the word ``using'', capitalize the ``u''; if not, keep using lower-cased.
\item Be aware of the different meanings of the homophones ``affect'' and ``effect'', ``complement'' and ``compliment'', ``discreet'' and ``discrete'', ``principal'' and ``principle''.
\item Do not confuse ``imply'' and ``infer''.
\item The prefix ``non'' is not a word; it should be joined to the word it modifies, usually without a hyphen.
\item There is no period after the ``et'' in the Latin abbreviation ``et al.''.
\item The abbreviation ``i.e.'' means ``that is'', and the abbreviation ``e.g.'' means ``for example''.
\end{itemize}
An excellent style manual for science writers is \cite{b7}.

\subsection{Authors and Affiliations}
\textbf{The class file is designed for, but not limited to, six authors.} A
minimum of one author is required for all conference articles. Author names
should be listed starting from left to right and then moving down to the
next line. This is the author sequence that will be used in future citations
and by indexing services. Names should not be listed in columns nor group by
affiliation. Please keep your affiliations as succinct as possible (for
example, do not differentiate among departments of the same organization).

\subsection{Identify the Headings}
Headings, or heads, are organizational devices that guide the reader through
your paper. There are two types: component heads and text heads.

Component heads identify the different components of your paper and are not
topically subordinate to each other. Examples include Acknowledgments and
References and, for these, the correct style to use is ``Heading 5''. Use
``figure caption'' for your Figure captions, and ``table head'' for your
table title. Run-in heads, such as ``Abstract'', will require you to apply a
style (in this case, italic) in addition to the style provided by the drop
down menu to differentiate the head from the text.

Text heads organize the topics on a relational, hierarchical basis. For
example, the paper title is the primary text head because all subsequent
material relates and elaborates on this one topic. If there are two or more
sub-topics, the next level head (uppercase Roman numerals) should be used
and, conversely, if there are not at least two sub-topics, then no subheads
should be introduced.

\subsection{Figures and Tables}
\paragraph{Positioning Figures and Tables} Place figures and tables at the top and
bottom of columns. Avoid placing them in the middle of columns. Large
figures and tables may span across both columns. Figure captions should be
below the figures; table heads should appear above the tables. Insert
figures and tables after they are cited in the text. Use the abbreviation
``Fig.~\ref{fig}'', even at the beginning of a sentence.

\begin{table}[htbp]
\caption{Table Type Styles}
\begin{center}
\begin{tabular}{|c|c|c|c|}
\hline
\textbf{Table}&\multicolumn{3}{|c|}{\textbf{Table Column Head}} \\
\cline{2-4}
\textbf{Head} & \textbf{\textit{Table column subhead}}& \textbf{\textit{Subhead}}& \textbf{\textit{Subhead}} \\
\hline
copy& More table copy$^{\mathrm{a}}$& &  \\
\hline
\multicolumn{4}{l}{$^{\mathrm{a}}$Sample of a Table footnote.}
\end{tabular}
\label{tab1}
\end{center}
\end{table}

\begin{figure}[htbp]
\centerline{\includegraphics{fig1.png}}
\caption{Example of a figure caption.}
\label{fig}
\end{figure}

Figure Labels: Use 8 point Times New Roman for Figure labels. Use words
rather than symbols or abbreviations when writing Figure axis labels to
avoid confusing the reader. As an example, write the quantity
``Magnetization'', or ``Magnetization, M'', not just ``M''. If including
units in the label, present them within parentheses. Do not label axes only
with units. In the example, write ``Magnetization (A/m)'' or ``Magnetization
\{A[m(1)]\}'', not just ``A/m''. Do not label axes with a ratio of
quantities and units. For example, write ``Temperature (K)'', not
``Temperature/K''.

\section*{Acknowledgment}

The preferred spelling of the word ``acknowledgment'' in America is without
an ``e'' after the ``g''. Avoid the stilted expression ``one of us (R. B.
G.) thanks $\ldots$''. Instead, try ``R. B. G. thanks$\ldots$''. Put sponsor
acknowledgments in the unnumbered footnote on the first page.

\section*{References}

Please number citations consecutively within brackets \cite{b1}. The
sentence punctuation follows the bracket \cite{b2}. Refer simply to the reference
number, as in \cite{b3}---do not use ``Ref. \cite{b3}'' or ``reference \cite{b3}'' except at
the beginning of a sentence: ``Reference \cite{b3} was the first $\ldots$''

Number footnotes separately in superscripts. Place the actual footnote at
the bottom of the column in which it was cited. Do not put footnotes in the
abstract or reference list. Use letters for table footnotes.

Unless there are six authors or more give all authors' names; do not use
``et al.''. Papers that have not been published, even if they have been
submitted for publication, should be cited as ``unpublished'' \cite{b4}. Papers
that have been accepted for publication should be cited as ``in press'' \cite{b5}.
Capitalize only the first word in a paper title, except for proper nouns and
element symbols.

For papers published in translation journals, please give the English
citation first, followed by the original foreign-language citation \cite{b6}.

\begin{thebibliography}{00}
\bibitem{retfound2023nature} Y. Zhou, et al., ``A foundation model for generalizable disease detection from retinal images,'' Nature, vol. 622, pp. 156--163, 2023.

\bibitem{badar2025transformer} D. Badar, J. Abbas, R. Alsini, T. Abbas, W. ChengLiang, and A. Daud, ``Transformer attention fusion for fine grained medical image classification,'' Scientific Reports, vol. 15, no. 1, pp. 1--16, 2025.

\bibitem{guo2025mainet} Y. Guo, C. Yang, Z. Zhang, Y. Zhang, F. Ma, and J. Meng, ``MAINet: Multi-scale attention interaction network for diabetic retinopathy lesion segmentation,'' Expert Systems with Applications, vol. 296, Part D, pp. 129247, 2026.

\bibitem{sun2023msca} Y. Sun, S. Dai, M. Zhang, B. Wang, C. Xu, and S. Lian, ``Msca-net: Multi-scale contextual attention network for skin lesion segmentation,'' Pattern Recognition, vol. 144, pp. 109830, 2024.

\bibitem{cao2022swin} H. Cao, Y. Wang, J. Chen, D. Jiang, X. Zhang, Q. Tian, and M. Wang, ``Swin-unet: Unet-like pure transformer for medical image segmentation,'' in European Conference on Computer Vision, pp. 205--218, 2022.

\bibitem{li2024multi} F. Li, H. Sheng, D. Wei, B. Tang, and C. Zou, ``Multi-lesion segmentation guided deep attention network for automated detection of diabetic retinopathy,'' Computers in Biology and Medicine, vol. 165, pp. 107418, 2024.

\bibitem{wang2023clc} X. Wang, S. Fang, Y. Yang, D. Zhu, B. Wang, Y. Zhang, H. Zhang, J. Cheng, H. Tong, and X. Han, ``Clc-net: Contextual and local collaborative network for lesion segmentation in diabetic retinopathy images,'' Neurocomputing, vol. 531, pp. 174--185, 2023.

\bibitem{sambyal2020modified} N. Sambyal, P. Saini, R. Syal, and V. Gupta, ``Modified u-net architecture for semantic segmentation of diabetic retinopathy images,'' Biocybernetics and Biomedical Engineering, vol. 40, no. 3, pp. 1094--1109, 2020.

\bibitem{bhati2024interpretable} A. Bhati, M. Gour, P. Khanna, A. Ojha, and N. Werghi, ``An interpretable dual attention network for diabetic retinopathy grading: IDANet,'' Artificial Intelligence in Medicine, vol. 150, pp. 102816, 2024.

\bibitem{liu2023automated} Q. Liu, H. Li, J. Kang, S. Li, R. Zhang, and S. Wang, ``Automated lesion segmentation in fundus images with many-to-many reassembly of features,'' Pattern Recognition, vol. 136, pp. 109247, 2023.

\bibitem{huang2024deep} S. Huang, J. Li, Y. Lin, D. Liu, H. Xu, X. Liu, C. Huang, H. Ding, H. Tu, and J. Yuan, ``Deep hierarchies and invariant disease-indicative feature learning for fundus disease classification,'' in Proceedings of the AAAI Conference on Artificial Intelligence, vol. 38, no. 2, pp. 1234--1242, 2024.
\end{thebibliography}
\vspace{12pt}
\color{red}
IEEE conference templates contain guidance text for composing and formatting conference papers. Please ensure that all template text is removed from your conference paper prior to submission to the conference. Failure to remove the template text from your paper may result in your paper not being published.

\end{document}