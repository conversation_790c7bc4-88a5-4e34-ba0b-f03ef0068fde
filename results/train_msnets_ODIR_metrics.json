{"timestamp": "2025-08-22T22:53:22.278367", "test_results": {"accuracy": 0.6886446886446886, "auc": 0.7559473493539428, "precision": 0.6963260860458297, "recall": 0.6886446886446886, "f1": 0.6855691056910569}, "training_history": {"train_loss": [0.6452277355556246, 0.6049014431766316, 0.5852168232579774, 0.5875006970725482, 0.5683433802067479, 0.5599788559388511, 0.538280252037169, 0.5461355953276912, 0.5376371352732936, 0.5342088156863104], "val_accuracy": [0.652014652014652, 0.6611721611721612, 0.663003663003663, 0.6538461538461539, 0.663003663003663, 0.6868131868131868, 0.6776556776556777, 0.6703296703296703, 0.6758241758241759, 0.6794871794871795], "val_auc": [0.7175461900736626, 0.7357136148344939, 0.7367601873096378, 0.7455621301775148, 0.7410135651893895, 0.7534516765285996, 0.7484066604945726, 0.7502448711239921, 0.7481114733861988, 0.7516805538783561], "val_precision": [0.6602815307349509, 0.6619566389817712, 0.683887080905169, 0.7120192307692308, 0.7108053237085495, 0.7038864807872539, 0.6925806873881867, 0.6854700854700855, 0.6901776384535004, 0.6889193311490227], "val_recall": [0.652014652014652, 0.6611721611721612, 0.663003663003663, 0.6538461538461539, 0.663003663003663, 0.6868131868131868, 0.6776556776556777, 0.6703296703296703, 0.6758241758241759, 0.6794871794871795], "val_f1": [0.6474690082644629, 0.6607613642088294, 0.6531562012345851, 0.6283533873308674, 0.6427514792899409, 0.6801164882226981, 0.671286857768352, 0.6634615384615384, 0.6695898335333395, 0.6754360637918443], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 6, "best_auc": 0.7534516765285996}, "training_config": {"model": "MSNets (Multi-Scale Networks)", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}