{"timestamp": "2025-08-22T23:22:58.564404", "test_results": {"accuracy": 0.63003663003663, "auc": 0.7030820217633404, "precision": 0.6308866229995272, "recall": 0.63003663003663, "f1": 0.6294350069884959}, "training_history": {"train_loss": [0.6897447554371025, 0.6842227736605874, 0.6756972934626326, 0.6659309056740773, 0.6571070687680305, 0.6498050908499127, 0.6414476972592028, 0.635688270194621, 0.6351580431189718, 0.6359984452211405], "val_accuracy": [0.5512820512820513, 0.5860805860805861, 0.6007326007326007, 0.5989010989010989, 0.5989010989010989, 0.608058608058608, 0.6135531135531136, 0.6318681318681318, 0.6245421245421245, 0.6263736263736264], "val_auc": [0.5887775228434569, 0.6301708059949819, 0.6536650163023789, 0.6617826617826619, 0.6676729863543049, 0.6712152316547921, 0.6772128969931168, 0.6803794496102189, 0.6827141112855398, 0.6857196527526197], "val_precision": [0.5552312138728324, 0.5904496045341117, 0.6021359091218286, 0.608448093220339, 0.5989024259338772, 0.6106782106782106, 0.6141058138280661, 0.6331707317073172, 0.6248251748251747, 0.6264007622830915], "val_recall": [0.5512820512820513, 0.5860805860805861, 0.6007326007326007, 0.5989010989010989, 0.5989010989010989, 0.608058608058608, 0.6135531135531136, 0.6318681318681318, 0.6245421245421245, 0.6263736263736264], "val_f1": [0.5431149607052226, 0.5810210650694699, 0.5993564283117687, 0.5898749807068993, 0.5988997534508494, 0.6057256235827665, 0.6130845829625028, 0.630965711345822, 0.6243291592128802, 0.6263535726266354], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 10, "best_auc": 0.6857196527526197}, "training_config": {"model": "SE-ResNet50", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}