{"timestamp": "2025-08-22T23:22:58.439396", "test_results": {"accuracy": 0.7459016393442623, "qwk": 0.723415845549948, "auc": 0.8552651857916496, "precision": 0.6216007125843191, "recall": 0.7459016393442623, "f1": 0.6696536654940688}, "training_history": {"train_loss": [1.3491903213055239, 1.1095941053784413, 0.929177177341088, 0.8551627365143403, 0.8077314554349236, 0.7969811263939609, 0.7745880011631094, 0.7682144528497821, 0.7565746456384659, 0.7548633826815564], "val_accuracy": [0.46994535519125685, 0.6229508196721312, 0.6885245901639344, 0.7049180327868853, 0.6994535519125683, 0.6994535519125683, 0.7049180327868853, 0.7049180327868853, 0.7076502732240437, 0.7076502732240437], "val_qwk": [0.0, 0.3941106959215611, 0.6253981806837213, 0.6838334818923113, 0.6757782839787396, 0.6870345238489293, 0.6832059806159241, 0.6951110522657311, 0.7106895293688436, 0.7106895293688436], "val_auc": [0.7459546044542364, 0.8196097906254668, 0.8392613602716752, 0.8464008813883813, 0.8557123803270699, 0.8620202838904939, 0.8663230131722074, 0.8703907396836128, 0.8669319821237614, 0.8665073829614418], "val_precision": [0.22084863686583653, 0.4616016333809452, 0.5315617667246635, 0.5507268417934031, 0.5466653499242873, 0.5506274641003943, 0.5534979292156624, 0.5546966327514612, 0.5601242646092501, 0.5601242646092501], "val_recall": [0.46994535519125685, 0.6229508196721312, 0.6885245901639344, 0.7049180327868853, 0.6994535519125683, 0.6994535519125683, 0.7049180327868853, 0.7049180327868853, 0.7076502732240437, 0.7076502732240437], "val_f1": [0.3004855059215471, 0.528244122971082, 0.5983441898451548, 0.6151673619679966, 0.610535936959346, 0.6120821215573857, 0.6162021963701292, 0.6167102819297543, 0.6203538333113089, 0.6203538333113089], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 9, "best_qwk": 0.7106895293688436}, "training_config": {"model": "SE-ResNet50", "num_classes": 5, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}