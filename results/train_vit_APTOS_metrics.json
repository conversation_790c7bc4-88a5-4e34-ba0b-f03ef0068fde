{"timestamp": "2025-08-22T23:30:22.087322", "test_results": {"accuracy": 0.7650273224043715, "qwk": 0.7524524387081026, "auc": 0.8861392002057185, "precision": 0.7549104432301154, "recall": 0.7650273224043715, "f1": 0.7150697081864374}, "training_history": {"train_loss": [1.265731058690859, 0.9796028189037157, 0.8829219263532887, 0.8200770904836447, 0.7827260578456132, 0.7635238620897998, 0.7410640114027521, 0.7329620919797731, 0.7384866113248079, 0.7283280235917672], "val_accuracy": [0.6147540983606558, 0.6612021857923497, 0.6994535519125683, 0.7103825136612022, 0.7158469945355191, 0.726775956284153, 0.7213114754098361, 0.7185792349726776, 0.7185792349726776, 0.7185792349726776], "val_qwk": [0.5094022012804834, 0.6089913327834933, 0.6612831279897866, 0.6852444176222088, 0.7088037396416176, 0.7118195063799726, 0.7241297232966379, 0.7152984964404927, 0.7152984964404927, 0.7152984964404927], "val_auc": [0.7279440106081416, 0.8133003841717736, 0.8410701513777024, 0.8550695585636945, 0.865887305708495, 0.8712803287187549, 0.8725955147721294, 0.8749909644701347, 0.8759019381665543, 0.8760306626477113], "val_precision": [0.5010067592775473, 0.5758935466167466, 0.628344645849153, 0.6507302675926864, 0.6840441685278751, 0.7004449340749905, 0.6985041673566263, 0.6906831613144044, 0.6906831613144044, 0.6906831613144044], "val_recall": [0.6147540983606558, 0.6612021857923497, 0.6994535519125683, 0.7103825136612022, 0.7158469945355191, 0.726775956284153, 0.7213114754098361, 0.7185792349726776, 0.7185792349726776, 0.7185792349726776], "val_f1": [0.5432817328693658, 0.6014438919163946, 0.6425879833878723, 0.6551677906450283, 0.6679502955970694, 0.6874971391054222, 0.6708948800717969, 0.6704016944013476, 0.6704016944013476, 0.6704016944013476], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 7, "best_qwk": 0.7241297232966379}, "training_config": {"model": "Vision Transformer Base", "num_classes": 5, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}