{"timestamp": "2025-08-22T22:53:22.128837", "test_results": {"accuracy": 0.7896174863387978, "qwk": 0.7950377226413626, "auc": 0.9070014873229824, "precision": 0.7689327586638527, "recall": 0.7896174863387978, "f1": 0.7465125052069883}, "training_history": {"train_loss": [1.1405296636664348, 0.7871310872876126, 0.7074462010160737, 0.664146918317546, 0.6437339471734088, 0.6130483474420465, 0.6017274150381917, 0.60130516115738, 0.588277581917203, 0.5720935894743256], "val_accuracy": [0.7049180327868853, 0.7595628415300546, 0.7486338797814208, 0.7786885245901639, 0.7540983606557377, 0.773224043715847, 0.76775956284153, 0.7622950819672131, 0.773224043715847, 0.7759562841530054], "val_qwk": [0.6955141978777561, 0.7734944639481501, 0.7855670103092783, 0.8006807351940095, 0.789981772767164, 0.7854382561239136, 0.7751388176006719, 0.7712546874039466, 0.7974360954513862, 0.8080244329721098], "val_auc": [0.8525146228033217, 0.8852105128891982, 0.9079009167643278, 0.9109974585441047, 0.9051746758784829, 0.9094249691749521, 0.9121165459778636, 0.9141163132287338, 0.9142338266104874, 0.9142085683964275], "val_precision": [0.5536946356618488, 0.6893764314478495, 0.6772869006822883, 0.703513027495238, 0.6850199267366669, 0.7212970518347359, 0.7213161199379724, 0.710384223137507, 0.7234381028257354, 0.7339080213288435], "val_recall": [0.7049180327868853, 0.7595628415300546, 0.7486338797814208, 0.7786885245901639, 0.7540983606557377, 0.773224043715847, 0.76775956284153, 0.7622950819672131, 0.773224043715847, 0.7759562841530054], "val_f1": [0.6166071568193072, 0.6982170273663951, 0.6849377065930851, 0.7238683378122635, 0.68723420982905, 0.7306689233330362, 0.7224959945479154, 0.7216516089772564, 0.7352934083959065, 0.7341194768215684], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 10, "best_qwk": 0.8080244329721098}, "training_config": {"model": "MSNets (Multi-Scale Networks)", "num_classes": 5, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}