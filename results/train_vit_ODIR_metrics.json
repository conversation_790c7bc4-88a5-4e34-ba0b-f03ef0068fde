{"timestamp": "2025-08-22T23:37:59.286004", "test_results": {"accuracy": 0.6593406593406593, "auc": 0.7248319446121645, "precision": 0.6605817208226846, "recall": 0.6593406593406593, "f1": 0.6586811857229281}, "training_history": {"train_loss": [0.8256972967823849, 0.6875620627705055, 0.6544218033174926, 0.6414796793008153, 0.6359041140803808, 0.623594447781768, 0.6232635597639447, 0.6164096974873845, 0.61572037165678, 0.6142482757568359], "val_accuracy": [0.5457875457875457, 0.6263736263736264, 0.6428571428571429, 0.6538461538461539, 0.6318681318681318, 0.673992673992674, 0.6684981684981685, 0.6684981684981685, 0.6684981684981685, 0.6684981684981685], "val_auc": [0.5889519515893142, 0.6565900521944478, 0.686417367736049, 0.7009217888338767, 0.707939191455675, 0.7133196473855814, 0.7156811442525728, 0.7164593648110131, 0.7169155630694092, 0.7172644205611239], "val_precision": [0.5466922077033591, 0.6288704932612712, 0.6526057791537668, 0.6656936416184971, 0.6340718105423988, 0.6805429864253395, 0.6756265383754756, 0.6723863386777948, 0.6723863386777948, 0.6723863386777948], "val_recall": [0.5457875457875457, 0.6263736263736264, 0.6428571428571429, 0.6538461538461539, 0.6318681318681318, 0.673992673992674, 0.6684981684981685, 0.6684981684981685, 0.6684981684981685, 0.6684981684981685], "val_f1": [0.5435767446877359, 0.6245550641786215, 0.6370608989108759, 0.6475458268297432, 0.6303491853912716, 0.6710086252420349, 0.6650999183302778, 0.6666183143016177, 0.6666183143016177, 0.6666183143016177], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 10, "best_auc": 0.7172644205611239}, "training_config": {"model": "Vision Transformer Base", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}