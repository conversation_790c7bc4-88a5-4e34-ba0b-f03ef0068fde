{"timestamp": "2025-08-22T22:53:46.241893", "test_results": {"accuracy": 0.5531135531135531, "auc": 0.5400313971742543, "precision": 0.553762783685776, "recall": 0.5531135531135531, "f1": 0.5517603359173127}, "training_history": {"train_loss": [2.7529209490063824, 2.606719148309925, 2.6155819983422, 2.507520496090756, 2.4271963804583008, 2.457828180699409, 2.4427660084977934, 2.398226520683192, 2.397746811939191, 2.3171873002112666], "val_accuracy": [0.4542124542124542, 0.46153846153846156, 0.46886446886446886, 0.47802197802197804, 0.47435897435897434, 0.47619047619047616, 0.4706959706959707, 0.48534798534798534, 0.4652014652014652, 0.48534798534798534], "val_auc": [0.41071260851480634, 0.4362328757933154, 0.43148304686766226, 0.4363670517516672, 0.4414858645627876, 0.44472621395698314, 0.4464839190113915, 0.44691328207811726, 0.44623569348844083, 0.448174536086624], "val_precision": [0.454209996645421, 0.46106832905512773, 0.4687571526665141, 0.47789115646258506, 0.474281330749354, 0.47615848661140153, 0.4704864864864865, 0.48531468531468536, 0.46497400229590113, 0.48532415869261375], "val_recall": [0.4542124542124542, 0.46153846153846156, 0.46886446886446886, 0.47802197802197804, 0.47435897435897434, 0.47619047619047616, 0.4706959706959707, 0.48534798534798534, 0.4652014652014652, 0.48534798534798534], "val_f1": [0.4542051309574925, 0.4599079457364341, 0.4684079769019003, 0.47724867724867726, 0.4739619525262596, 0.4760147106195641, 0.4697550632252081, 0.4850560670186308, 0.46433179228039995, 0.4851390123995369], "learning_rate": [0.0001, 9.755282581475769e-05, 9.045084971874737e-05, 7.938926261462366e-05, 6.545084971874737e-05, 4.9999999999999996e-05, 3.454915028125263e-05, 2.0610737385376345e-05, 9.549150281252631e-06, 2.447174185242323e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "best_epoch": 10, "best_auc": 0.448174536086624}, "training_config": {"model": "EfficientNet-B0", "num_classes": 2, "epochs": 10, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}