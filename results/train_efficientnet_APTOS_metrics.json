{"timestamp": "2025-08-22T22:50:44.094126", "test_results": {"accuracy": 0.4890710382513661, "qwk": 0.3133595700562821, "auc": 0.6488369214157196, "precision": 0.5193975691901374, "recall": 0.4890710382513661, "f1": 0.5001370926851776}, "training_history": {"train_loss": [4.28837425812431, 3.49123898278112, 3.0861686571784643, 2.7234708705674047, 2.508477041254873, 2.4011163815208105, 2.2712097012478374, 2.209742106821226, 2.1293255583099695, 2.0675267058870066, 2.0517835085806637, 2.040725369816241, 2.0100315679674563, 2.001521065183308, 1.9558279255162114], "val_accuracy": [0.1721311475409836, 0.2185792349726776, 0.2568306010928962, 0.29508196721311475, 0.319672131147541, 0.34153005464480873, 0.37158469945355194, 0.3907103825136612, 0.3879781420765027, 0.42349726775956287, 0.43169398907103823, 0.4344262295081967, 0.42896174863387976, 0.4453551912568306, 0.4262295081967213], "val_qwk": [-0.0920435414016012, -0.05603757495364037, -0.04173001737619453, -0.01615858737298015, 0.07304904721741745, 0.13181331379290906, 0.14833609016232585, 0.24431661764492807, 0.22503304807625413, 0.2755714468139445, 0.29144481479878215, 0.3040631344179796, 0.25479009997983126, 0.3072388383181621, 0.32505827178522273], "val_auc": [0.4285798111703543, 0.4456637714005696, 0.4662129179040285, 0.5021220992744716, 0.5166536106335082, 0.5345055049727018, 0.5517906456499417, 0.570048492461412, 0.5721326817395566, 0.5837228936248436, 0.5822422930522868, 0.5881471273990939, 0.5853488258592359, 0.5879238603743787, 0.5914872626796248], "val_precision": [0.22897880690088582, 0.2504692341890477, 0.2669278182925245, 0.3159776726212745, 0.3349957104795291, 0.34192667846789293, 0.38005039884888, 0.40353100882045145, 0.40244250081505745, 0.4329448957196682, 0.4410802869127022, 0.44079278341573425, 0.4344595435409402, 0.4455546707992716, 0.4469408377736885], "val_recall": [0.17213114754098358, 0.2185792349726776, 0.2568306010928962, 0.29508196721311475, 0.319672131147541, 0.34153005464480873, 0.37158469945355194, 0.3907103825136612, 0.3879781420765027, 0.42349726775956287, 0.43169398907103823, 0.4344262295081967, 0.42896174863387976, 0.4453551912568306, 0.4262295081967213], "val_f1": [0.18801467922068565, 0.22982286362579776, 0.25912455944188184, 0.3025010756442438, 0.3242005834076739, 0.33850212834901977, 0.3731872825229854, 0.3948406238879116, 0.39174739882246357, 0.42564218458239167, 0.4347210476733899, 0.433855989183858, 0.4262531848687942, 0.44138886106977415, 0.4340575572087035], "learning_rate": [0.0001, 9.890738003669029e-05, 9.567727288213005e-05, 9.045084971874738e-05, 8.345653031794292e-05, 7.500000000000001e-05, 6.545084971874738e-05, 5.522642316338269e-05, 4.4773576836617344e-05, 3.454915028125264e-05, 2.5000000000000018e-05, 1.654346968205711e-05, 9.549150281252635e-06, 4.322727117869952e-06, 1.092619963309716e-06], "epochs": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "best_epoch": 15, "best_qwk": 0.32505827178522273}, "training_config": {"model": "EfficientNet-B0", "num_classes": 5, "epochs": 15, "batch_size": 32, "device": "cuda", "optimizer": "<PERSON>", "scheduler": "CosineAnnealingLR", "criterion": "CrossEntropyLoss"}}